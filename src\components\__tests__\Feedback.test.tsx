import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Feedback from '../Feedback';

// Mock the feedback service
jest.mock('../../services/feedbackService', () => ({
  feedbackService: {
    submitFeedback: jest.fn()
  }
}));

// Mock GSAP
jest.mock('gsap', () => ({
  fromTo: jest.fn(),
  to: jest.fn(),
  timeline: jest.fn(() => ({
    fromTo: jest.fn().mockReturnThis(),
    to: jest.fn().mockReturnThis()
  }))
}));

const renderFeedback = () => {
  return render(
    <BrowserRouter>
      <Feedback />
    </BrowserRouter>
  );
};

describe('Feedback Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders contact information fields', () => {
    renderFeedback();
    
    expect(screen.getByText('Contact Information')).toBeInTheDocument();
    expect(screen.getByText('Email Address *')).toBeInTheDocument();
    expect(screen.getByText('Full Name *')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your full name')).toBeInTheDocument();
  });

  test('renders required and optional questions correctly', () => {
    renderFeedback();
    
    // Check required questions have asterisks
    expect(screen.getByText(/1\. When you consider writing.*\*/)).toBeInTheDocument();
    expect(screen.getByText(/4\. In today's increasingly digital world.*\*/)).toBeInTheDocument();
    expect(screen.getByText(/5\. Imagine a platform.*\*/)).toBeInTheDocument();
    
    // Check optional questions are marked as optional
    expect(screen.getByText(/3\. Regarding monetizing.*\(Optional\)/)).toBeInTheDocument();
    expect(screen.getByText(/4\. Beyond your day-to-day.*\(Optional\)/)).toBeInTheDocument();
  });

  test('validates required fields', async () => {
    renderFeedback();
    
    const submitButton = screen.getByText('Submit Feedback');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter your email address')).toBeInTheDocument();
      expect(screen.getByText('Please enter your name')).toBeInTheDocument();
      expect(screen.getByText('Please select your biggest hurdle')).toBeInTheDocument();
      expect(screen.getByText('Please select your biggest professional fear')).toBeInTheDocument();
      expect(screen.getByText('Please provide your answer for platform impact')).toBeInTheDocument();
    });
  });

  test('validates email format', async () => {
    renderFeedback();
    
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    
    const submitButton = screen.getByText('Submit Feedback');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });
  });

  test('validates minimum character length for text fields', async () => {
    renderFeedback();
    
    // Fill required fields
    fireEvent.change(screen.getByPlaceholderText('Enter your email address'), { 
      target: { value: '<EMAIL>' } 
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your full name'), { 
      target: { value: 'Test User' } 
    });
    
    // Select required multiple choice options
    const hurdleOption = screen.getByDisplayValue('A');
    fireEvent.click(hurdleOption);
    
    const fearOption = screen.getAllByDisplayValue('A')[3]; // Professional fear question
    fireEvent.click(fearOption);
    
    // Add short text to platform impact (required field)
    const platformTextarea = screen.getByPlaceholderText('Please provide your detailed answer...');
    fireEvent.change(platformTextarea, { target: { value: 'Short' } });
    
    const submitButton = screen.getByText('Submit Feedback');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter at least 10 characters')).toBeInTheDocument();
    });
  });

  test('allows submission with only required fields filled', async () => {
    const mockSubmit = require('../../services/feedbackService').feedbackService.submitFeedback;
    mockSubmit.mockResolvedValue({ success: true, message: 'Success' });
    
    renderFeedback();
    
    // Fill required fields
    fireEvent.change(screen.getByPlaceholderText('Enter your email address'), { 
      target: { value: '<EMAIL>' } 
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your full name'), { 
      target: { value: 'Test User' } 
    });
    
    // Select required multiple choice options
    const hurdleOption = screen.getByDisplayValue('A');
    fireEvent.click(hurdleOption);
    
    const fearOption = screen.getAllByDisplayValue('A')[3]; // Professional fear question
    fireEvent.click(fearOption);
    
    // Add valid text to platform impact (required field)
    const platformTextarea = screen.getByPlaceholderText('Please provide your detailed answer...');
    fireEvent.change(platformTextarea, { target: { value: 'This is a valid response with more than 10 characters' } });
    
    const submitButton = screen.getByText('Submit Feedback');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'Test User',
        biggest_hurdle: 'A',
        biggest_hurdle_other: undefined,
        primary_motivation: undefined,
        time_consuming_part: undefined,
        professional_fear: 'A',
        monetization_considerations: undefined,
        professional_legacy: undefined,
        platform_impact: 'This is a valid response with more than 10 characters'
      });
    });
  });
});
